#!/bin/bash

echo "Fixing Android build for React Native 0.80.1..."

# Fix app build.gradle
sed -i '' 's/apply from: "..\/..\/node_modules\/react-native\/react.gradle"/\/\/ react.gradle is not needed in React Native 0.80.1/' android/app/build.gradle

# Fix compileSdk
sed -i '' 's/compileSdkVersion rootProject.ext.compileSdkVersion/compileSdk rootProject.ext.compileSdkVersion/' android/app/build.gradle

# Add namespace and buildFeatures
sed -i '' '/android {/a\
    namespace "com.ModrkClient"\
    ndkVersion rootProject.ext.ndkVersion\
\
    compileSdk rootProject.ext.compileSdkVersion\
\
    buildFeatures {\
        buildConfig true\
    }
' android/app/build.gradle

# Remove manual project implementations
sed -i '' 's/implementation project.*react-native-video.*/\/\/ react-native-video is auto-linked in React Native 0.80.1/' android/app/build.gradle
sed -i '' 's/implementation project.*react-native-push-notification.*/\/\/ react-native-push-notification is auto-linked in React Native 0.80.1/' android/app/build.gradle
sed -i '' 's/implementation project.*react-native-audio-recorder-player.*/\/\/ react-native-audio-recorder-player is auto-linked in React Native 0.80.1/' android/app/build.gradle

# Fix AppsFlyer dependency
sed -i '' "s/implementation 'com.appsflyer:af-android-sdk'/implementation 'com.appsflyer:af-android-sdk:6.9.4'/" android/app/build.gradle

# Remove React Native dependency
sed -i '' 's/implementation "com.facebook.react:react-native:+"/\/\/ React Native dependency is handled by the React Native Gradle plugin in 0.80.1/' android/app/build.gradle

# Remove native_modules.gradle references
sed -i '' 's/apply from: file.*native_modules.gradle.*/\/\/ Auto-linking is handled by React Native 0.80.1 automatically/' android/app/build.gradle
sed -i '' 's/applyNativeModulesAppBuildGradle(project)//' android/app/build.gradle

echo "Android build fixes applied!"
echo "Running Android build..."

npx react-native run-android
