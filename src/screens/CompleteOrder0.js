import React, { useEffect, useRef, useState, useCallback } from 'react';
import { View, Text, Image, BackHandler, ImageBackground, StyleSheet, I18nManager, Pressable, Keyboard, Modal } from "react-native";
import { appFont, appFontBold, Green, WhiteGreen, screenWidth, White, DarkGreen, Blue, MediumGrey, DarkGrey, WhiteGery, screenHeight, appColor1, Black, appColor2, LightGreen, Red, Red1 } from '../components/Styles';
import { Button, Input } from 'native-base';
import { ScrollView, TouchableOpacity } from 'react-native-gesture-handler';
import Header from '../components/Header';
import { strings } from './i18n';
import MyFooter from '../components/MyFooter';
import { KeyboardAwareScrollView, ScrollableComponent } from 'react-native-keyboard-aware-scroll-view';
import Loading from '../components/Loading';
import { useDispatch, useSelector } from 'react-redux';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import * as ordersActions from '../../Store/Actions/orders';
import Toaster from '../components/Toaster';
import LoadingMore from '../components/LoadingMore';
import * as cartActions from '../../Store/Actions/cart';
import RNRestart from 'react-native-restart';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import BackgroundTimer from 'react-native-background-timer';




const CompleteOrder0 = (props) => {
    const [shared, setShared] = useState(props.route.params.shared);
    const [order, setOrder] = useState(props.route.params.order)

    const [cutId, setCutId] = useState(0)
    const [preperId, setPreperId] = useState(0)
    const [DATA, setDATA] = useState({})
    const [selectedSheeps, setSelectedSheeps] = useState([])
    const [selectedSheepsIds, setSelectedSheepsIds] = useState([])
    const [subTotal, setSubTotal] = useState(0)
    const [time, setTime] = useState({})
    const [seconds, setSeconds] = useState(60)
    const [item, setItem] = useState('0')
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const minss = useSelector(state => state.cart.mins)
    const secss = useSelector(state => state.cart.secs)

    const [mins, setMins] = useState(minss)
    const [secs, setSecs] = useState(secss)
    const dispatch = useDispatch();

    const cartt = useSelector(state => state.cart.cart)
    const [cart, setCart] = useState(cartt);
    const [cartItems, setCartItems] = useState(shared ? order.sheep : cartt.sheep ? cartt.sheep : []);
    const [itemId, setItemId] = useState(shared ? order.sheep.length == 1 ? order.sheep[0].id : 0 : cartt.sheep ? cartt.sheep.length == 1 ? cartt.sheep[0].id : 0 : 0)
    const [note, setNote] = useState(cartt.note ? cartt.note : '')

    // const options = useSelector(state => state.orders.options || [])
    // const [cut, setCut] = useState(options?.cutting_options || [])
    // const [preper, setPreper] = useState(options?.preparing_options || [])
    // const [head, setHead] = useState(options?.head_options)
    const [cut, setCut] = useState([])
    const [preper, setPreper] = useState([])
    const [head, setHead] = useState([])
    const [selectedOptions, setSelectedOptions] = useState([])
    // const [selectedOptions, setSelectedOptions] = useState(shared ? [] : cartt.options_formatted)
    const IsFocused = useIsFocused();

    const [ModalVisible, setModalVisible] = useState(false);

    const intervalRef = useRef(null); // Store interval ID in a ref

    useFocusEffect(
        React.useCallback(() => {
            const onBack = () => {
                if (cartItems.length > 0) { setModalVisible(!ModalVisible) }
                else { props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' }); }
                return true;
            };

            const subscription = BackHandler.addEventListener(
                'hardwareBackPress',
                onBack
            );

            return () => subscription.remove();
        }, [])
    );



    const setOptions = async (selectedOption) => {
        console.log(itemId);
        let nextSelectedOptions = [...selectedOptions];
        console.log('nextSelectedOptions', nextSelectedOptions);
        if (itemId == 0) {
            let cartOption = selectedOptions.find(x => x.item_type === 'Cart');
            let cartOptionfindIndex = selectedOptions.findIndex(x => x.item_type === 'Cart');
            if (cartOption) {
                let cartOptionOption = cartOption.options.find(x => x.type === selectedOption.morph_class);
                let cartOptionOptionfindIndex = cartOption.options.findIndex(x => x.type === selectedOption.morph_class);
                if (cartOptionOption) {
                    nextSelectedOptions[cartOptionfindIndex].options[cartOptionOptionfindIndex].id = selectedOption.id
                    setSelectedOptions(nextSelectedOptions)
                }
                else {
                    nextSelectedOptions[cartOptionfindIndex].options = [...nextSelectedOptions[cartOptionfindIndex].options, { id: selectedOption.id, type: selectedOption.morph_class }]
                    setSelectedOptions(nextSelectedOptions)
                }
            }
            else {
                let option = {
                    item_id: shared ? order.id : cart.id,
                    item_type: 'Cart',
                    options: [
                        {
                            id: selectedOption.id,
                            type: selectedOption.morph_class
                        }
                    ]
                }
                setSelectedOptions([option, ...selectedOptions])
            }
        }
        else {
            let cartItemOption = selectedOptions.find(x => x.item_id === itemId);
            let cartItemOptionfindIndex = selectedOptions.findIndex(x => x.item_id === itemId);
            if (cartItemOption) {
                let cartItemOptionOption = cartItemOption.options.find(x => x.type === selectedOption.morph_class);
                let cartItemOptionOptionfindIndex = cartItemOption.options.findIndex(x => x.type === selectedOption.morph_class);
                if (cartItemOptionOption) {
                    nextSelectedOptions[cartItemOptionfindIndex].options[cartItemOptionOptionfindIndex].id = selectedOption.id
                    setSelectedOptions(nextSelectedOptions)
                }
                else {
                    nextSelectedOptions[cartItemOptionfindIndex].options = [...nextSelectedOptions[cartItemOptionfindIndex].options, { id: selectedOption.id, type: selectedOption.morph_class }]
                    setSelectedOptions(nextSelectedOptions)
                }
            }
            else {
                let option = {
                    item_id: itemId,
                    item_type: 'CartItem',
                    options: [
                        {
                            id: selectedOption.id,
                            type: selectedOption.morph_class
                        }
                    ]
                }
                setSelectedOptions([...selectedOptions, option])
            }
        }

        console.log('aksdjkalsd', selectedOptions);
    }


    const [timeLeft, setTimeLeft] = useState(props.route.params.timeLeft); // 10 minutes in seconds
    const timerRef = useRef(null);

    // Start Timer Function
    const startTimer = () => {
        if (timerRef.current) return;

        timerRef.current = BackgroundTimer.setInterval(() => {
            setTimeLeft((prev) => {
                if (prev <= 1) {
                    BackgroundTimer.clearInterval(timerRef.current);
                    timerRef.current = null;
                    handleTimerEnd();
                    props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
                    return 0;
                }
                return prev - 1;
            });

            if (cartItems.length == 0) {
                dispatch(cartActions.clearCart());
                BackgroundTimer.clearInterval(timerRef.current);
                timerRef.current = null;
                props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
            }
        }, 1000);
    };
    const handleTimerEnd = () => {
        Toaster(
            'top',
            'danger',
            Red1,
            strings('lang.message13'),
            White,
            4500,
            screenHeight / 15,
        );
        dispatch(cartActions.clearCart())
    };
    // Stop Timer Function
    const stopTimer = () => {
        if (timerRef.current) {
            BackgroundTimer.clearInterval(timerRef.current);
            timerRef.current = null;
        }
    };

    // Reset Timer to 10 minutes
    const resetTimer = () => {
        setTimeLeft(props.route.params.timeLeft);
    };

    // Start timer when screen is focused & Reset when leaving
    useFocusEffect(
        useCallback(() => {
            resetTimer(); // Reset every time you open the screen
            startTimer();

            // Cleanup when screen is unfocused (leaving screen)
            return () => {
                stopTimer();
                resetTimer(); // Reset when leaving the screen
            };
        }, [])
    );

    // Format time as mm:ss
    const formatTime = (seconds) => {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    };


    // useEffect(() => {

    //     intervalRef.current = setInterval(async () => {
    //         if (cartItems.length == 0) {
    //             // setMins(10)
    //             // setSecs(0)
    //             // Toaster(
    //             //     'top',
    //             //     'danger',
    //             //     Red1,
    //             //     strings('lang.message13'),
    //             //     White,
    //             //     4500,
    //             //     screenHeight / 15,
    //             // );
    //             clearInterval(intervalRef.current);
    //             // dispatch(cartActions.timer(10, 0));
    //             dispatch(cartActions.timer(10, 0));
    //             await dispatch(cartActions.clearCart())
    //             props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
    //             // RNRestart.Restart();
    //         } else {
    //             if (secs <= 0) {
    //                 if (mins <= 0) {
    //                     clearInterval(intervalRef.current);
    //                     Toaster(
    //                         'top',
    //                         'danger',
    //                         Red1,
    //                         strings('lang.message13'),
    //                         White,
    //                         4500,
    //                         screenHeight / 15,
    //                     );
    //                     clearInterval(intervalRef.current);
    //                     dispatch(cartActions.timer(10, 0));
    //                     await dispatch(cartActions.clearCart())
    //                     props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
    //                 }
    //                 else {
    //                     setMins(m => m - 1)
    //                     setSecs(59)

    //                     dispatch(cartActions.timer(mins - 1, 59));
    //                 }
    //             }
    //             else {
    //                 setSecs(s => s - 1)
    //                 dispatch(cartActions.timer(mins, secs - 1));
    //             }
    //         }
    //     }, 1000)
    //     return () => { clearInterval(intervalRef.current); }
    // }, [secs, mins]);

    useEffect(() => {
        console.log('timeLeft', props.route.params.timeLeft);
        const getOptions = async () => {
            try {
                setLoading(true)
                let response = await dispatch(ordersActions.getOptions(props.route.params.sheepTypeId));
                if (response.success == true) {
                    console.log('props.route.params.sheepTypeId', props.route.params.sheepTypeId);
                    setCut(response.data.cutting_options)
                    setPreper(response.data.preparing_options)
                    setHead(response.data.head_options)
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red1,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };
        getOptions()
        // if (options) {
        //     setCut(options.cutting_options || [])
        //     setPreper(options.preparing_options || [])
        //     setHead(options.head_options || [])
        // }
    }, []);

    const nav = async () => {
        setLoadingMore(true)
        if (shared) {
            for (let option of selectedOptions) {
                try {
                    let response = await dispatch(cartActions.selectPartnerOptions(option, order.id));
                    if (response.success == true) {
                        // setLoadingMore(false)
                        // props.navigation.navigate("CompleteOrder1", { shared: props.route.params.shared, order: props.route.params.order })

                    }
                    else {
                        setLoadingMore(false)
                    }
                }
                catch (err) {
                    console.log('err', err)
                }
            }
            setLoadingMore(false)
            props.navigation.push("CompleteOrder1", { shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft })
        }
        else {
            for (let option of selectedOptions) {
                try {
                    let response = await dispatch(cartActions.selectOptions(option));
                    if (response.success == true) {
                        // setLoadingMore(false)
                        // props.navigation.navigate("CompleteOrder1", { shared: props.route.params.shared, order: props.route.params.order })

                    }
                    else {
                        setLoadingMore(false)
                    }
                }
                catch (err) {
                    console.log('err', err)
                }
            }
            if (note != cartt.note) {
                try {
                    let response = await dispatch(cartActions.addNote(note));
                    if (response.success == true) {
                    }
                    else {
                    }
                }
                catch (err) {
                    setLoadingMore(false)

                    console.log('err', err)
                }
            }
            if (cutId == 0 || preperId == 0) {
                setLoadingMore(false)
                Toaster(
                    'top',
                    'danger',
                    Red1,
                    strings('lang.Choosetocutandpreparefirst'),
                    White,
                    4500,
                    screenHeight / 15,
                );
            } else {
                props.navigation.push("CompleteOrder1", { shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft })
                setSelectedOptions([])
                setLoadingMore(false)
            }
            setLoadingMore(false)


        }
    }


    const [keyboardIsOpen, setKeyboardIsOpen] = React.useState(false);
    Keyboard.addListener("keyboardDidShow", () => {
        setKeyboardIsOpen(true);
    });
    Keyboard.addListener("keyboardDidHide", () => {
        setKeyboardIsOpen(false);
    });

    const clearCart = async () => {
        setLoadingMore(true)
        try {
            let response = await dispatch(cartActions.clearCart());
            if (response.success == true) {
                setModalVisible(!ModalVisible)
                back();
            }
            else {
            }
            setLoadingMore(false)
        }
        catch (err) {
            console.log('err', err)
            setLoadingMore(false)
        }
    }


    const back = async () => {
        // setLoadingMore(true)

        await dispatch(cartActions.clearCart())
        if (intervalRef.current) {
            clearInterval(intervalRef.current); // Stop the interval
            intervalRef.current = null; // Reset the interval reference
            console.log('Interval stopped automatically.');
        }
        dispatch(cartActions.timer(10, 0));
        dispatch(cartActions.timerCart(1, 0));
        props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });

        // setLoadingMore(false)
    }


    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                {/* <Header title={strings('lang.Completetheapplication')} backPress={() => { if (shared) { setModalVisible(!ModalVisible) } else { props.navigation.goBack() } }} /> */}

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    < View style={{ marginTop: screenHeight / 50, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 15,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    <View style={{ marginTop: screenHeight / 100, flexDirection: 'row', width: '100%', paddingHorizontal: '1%', justifyContent: 'space-between' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 12,
                                    height: screenHeight / 22,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 5,
                                    marginStart: 5
                                }}
                            />
                        </SkeletonPlaceholder>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 8,
                                    height: screenHeight / 22,
                                    borderRadius: 5,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 8,
                                    height: screenHeight / 22,
                                    borderRadius: 5,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 7,
                                    height: screenHeight / 22,
                                    borderRadius: 5,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>

                    </View>

                    <View style={{ marginTop: screenHeight / 50, flexDirection: 'row', width: '100%', paddingHorizontal: '1%', justifyContent: 'space-between' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 6,
                                    height: screenHeight / 22,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                    marginStart: 5
                                }}
                            />
                        </SkeletonPlaceholder>

                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 6,
                                    height: screenHeight / 22,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                    marginStart: 5
                                }}
                            />
                        </SkeletonPlaceholder>

                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 6,
                                    height: screenHeight / 22,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                    marginStart: 5
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 50, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '20%',
                                    height: screenHeight / 25,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'flex-start'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 200, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20,
                                    marginBottom: '2%'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>
                    < View style={{ marginTop: screenHeight / 200, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20,
                                    marginBottom: '2%'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>
                    < View style={{ marginTop: screenHeight / 200, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20,
                                    marginBottom: '2%'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>
                    < View style={{ marginTop: screenHeight / 200, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20,
                                    marginBottom: '2%'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>
                    < View style={{ marginTop: screenHeight / 200, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20,
                                    marginBottom: '2%'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>
                    < View style={{ marginTop: screenHeight / 200, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20,
                                    marginBottom: '2%'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>
                    < View style={{ marginTop: screenHeight / 200, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20,
                                    marginBottom: '2%'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView >
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                {/* <Header title={strings('lang.Completetheapplication')}
                    backPress={() => { if (cartItems.length > 0) { setModalVisible(!ModalVisible) } else { props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' }); } }}
                //  backPress={() => { if (shared) { setModalVisible(!ModalVisible) } else { props.navigation.goBack() } }} 
                /> */}

                {loadingMore ? <LoadingMore /> : <></>}


                <KeyboardAwareScrollView
                    showsVerticalScrollIndicator={false}
                    style={{ width: '100%' }}
                >



                    <ScrollView
                        showsVerticalScrollIndicator={false}
                        style={{ width: '100%' }}
                    >
                        <View style={{
                            flexDirection: 'row', width: "100%", alignSelf: "center", justifyContent: 'space-between', alignItems: "flex-end", height: screenHeight / 9,
                            backgroundColor: Red,
                        }}>
                            <View style={{ width: "33%", height: "70%", alignItems: "flex-start", justifyContent: "center" }}>
                                <Text numberOfLines={1} style={{ fontSize: screenWidth / 35, fontFamily: appFont, color: White, }}>{strings('lang.Cuttingandprocessing')}</Text>
                                <View style={{ height: 6, width: '95%', backgroundColor: White, marginTop: 5, borderRadius: 4 }}></View>
                            </View>
                            <View style={{ width: "33%", height: "70%", alignItems: "flex-start", justifyContent: "center" }}>
                                <Text numberOfLines={1} style={{ fontSize: screenWidth / 35, fontFamily: appFont, color: DarkGrey }}>{strings('lang.Titleanddate')}</Text>
                                <View style={{ height: 6, width: '95%', backgroundColor: DarkGrey, marginTop: 5, borderRadius: 4, opacity: .5 }}></View>
                            </View>
                            <View style={{ width: "33%", height: "70%", alignItems: "flex-start", justifyContent: "center" }}>
                                <Text numberOfLines={1} style={{ fontSize: screenWidth / 35, fontFamily: appFont, color: DarkGrey }}>{strings('lang.payingoff')}</Text>
                                <View style={{ height: 6, width: '95%', backgroundColor: DarkGrey, marginTop: 5, borderRadius: 4, opacity: .5 }}></View>
                            </View>
                            {/* <View style={{ width: "25%", height: "70%", alignItems: "flex-start", justifyContent: "center" }}>
                                <Text numberOfLines={1} style={{ fontSize: screenWidth / 35, fontFamily: appFont, color: DarkGrey }}>{strings('lang.Orderdetails')}</Text>
                                <View style={{ height: 6, width: '95%', backgroundColor: DarkGrey, marginTop: 5, borderRadius: 4, opacity: .5 }}></View>
                            </View> */}
                        </View>
                    </ScrollView>

                    <View style={{ flexDirection: 'row', borderEndWidth: .5, borderRadius: 20, borderColor: WhiteGery, backgroundColor: WhiteGery, width: screenWidth / 1.1, height: screenHeight / 15, alignSelf: 'center', alignItems: 'center', justifyContent: 'center', marginVertical: 20 }}>
                        <Text style={{ color: Red1, fontFamily: appFontBold, fontSize: screenWidth / 35, }}>{strings('lang.messageFinshTime')} </Text>
                        <Image source={require('../images/newIcon/watch.png')} style={{ resizeMode: 'contain', width: '10%', height: '30%', tintColor: Red1 }} />
                        {/* <Text style={{ color: Red1, fontFamily: appFontBold, fontSize: screenWidth / 30, }}>{mins}:{secs < 10 && 0}{secs}</Text> */}
                        <Text style={{ color: Red1, fontFamily: appFontBold, fontSize: screenWidth / 30, }}>{formatTime(timeLeft)}</Text>
                    </View>

                    {/* <Text style={styles.title}>{strings('lang.Orders')}</Text> */}
                    {/* {order == '0' && */}
                    <View style={{ height: screenHeight / 15, width: '95%', alignItems: 'center', alignSelf: 'center', justifyContent: 'space-between', }}>
                        <ScrollView
                            horizontal={true}
                            showsHorizontalScrollIndicator={false}
                            style={{ alignSelf: "center", flexDirection: 'row', width: '100%', height: '50%', }}
                            contentContainerStyle={{ flexGrow: 1 }}
                        >
                            {cartItems.length == 1
                                ?
                                <></>
                                :
                                <Pressable
                                    onPress={() => { setItemId(0) }}
                                    style={itemId == 0 ? styles.activeButton1 : styles.Button1}>
                                    <Text style={itemId == 0 ? styles.activeLabel1 : styles.label1}>{strings('lang.All')}</Text>
                                </Pressable>
                            }

                            {cartItems.map((item, index) => {
                                return (
                                    <Pressable
                                        onPress={() => { setItemId(item.id) }}
                                        style={item.id == itemId ? styles.activeButton1 : styles.Button1}>

                                        <View style={{ height: screenWidth / 18, paddingHorizontal: 5, borderRadius: 25, backgroundColor: item.sheep.collar_color, alignItems: 'center', justifyContent: 'center', marginStart: 5 }}>
                                            <Text style={item.id == itemId ? styles.textWhite : [styles.textWhite, { color: Black }]}>{item.sheep.id}</Text>
                                        </View>
                                        <Text style={item.id == itemId ? styles.activeLabel1 : styles.label1}>{item.sheep.farm.weight.from}-{item.sheep.farm.weight.to} {strings('lang.kg')} / {item.sheep.farm.code}</Text>
                                        {/* <Text style={item.id == itemId ? styles.activeLabel1 : styles.label1}> {item.sheep.farm.code} / {item.sheep.farm.weight.from}-{item.sheep.farm.weight.to} {strings('lang.kg')}</Text> */}
                                    </Pressable>
                                )
                            })}
                        </ScrollView>
                    </View>
                    {/* } */}


                    {/* {order == '1' &&
                        <View style={{ flexDirection: 'row', width: screenWidth / 1.1, maxHeight: screenHeight / 9, borderWidth: 1, borderRadius: 7, borderColor: MediumGrey, marginVertical: '5%', alignItems: 'center', alignSelf: 'center', justifyContent: 'flex-start' }}>
                            <View style={{ width: 50, height: 50, backgroundColor: Red1, alignItems: 'center', justifyContent: 'center', marginEnd: '5%', marginStart: '2%', alignSelf: 'center', borderRadius: screenWidth / 60 }}>
                                <Text style={{ color: White, fontFamily: appFontBold, fontSize: screenWidth / 24, }}>1</Text>
                            </View>

                            <View style={{ flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                                <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 26, }}>25 {strings('lang.kg')} - {strings('lang.year')} 70</Text>
                                <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 26, marginHorizontal: '5%' }}>SR 1200</Text>
                            </View>
                        </View>
                    } */}
                    {cut && cut.length == 0
                        ?
                        <></>
                        :
                        <View style={{ marginVertical: '2%', flexDirection: 'column', width: '95%', alignSelf: "center", justifyContent: 'center', }}>
                            <Text style={styles.title2}>{strings('lang.chopping')}</Text>
                            {cut && cut.map((item, index) => {
                                return (
                                    // <Pressable
                                    //     onPress={() => { setOptions(item); setCutId(item.id) }}
                                    //     style={
                                    //         itemId == 0
                                    //             ?
                                    //             selectedOptions.find(x => x.item_id === shared ? order.id : cart.id)
                                    //                 ?
                                    //                 selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "CuttingOption").id == item.id

                                    //                 ?
                                    //                     styles.activeButton2
                                    //                     :
                                    //                     styles.Button2
                                    //                 :
                                    //                 styles.Button2
                                    //             :
                                    //             selectedOptions.find(x => x.item_id === itemId)
                                    //                 ?
                                    //                 selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "CuttingOption").id == item.id
                                    //                     ?
                                    //                     styles.activeButton2
                                    //                     :
                                    //                     styles.Button2
                                    //                 :
                                    //                 styles.Button2
                                    //     } >
                                    //     <Text
                                    //         style={
                                    //             itemId == 0
                                    //                 ?
                                    //                 selectedOptions.find(x => x.item_id === shared ? order.id : cart.id)
                                    //                     ?
                                    //                     selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "CuttingOption").id == item.id
                                    //                         ?
                                    //                         styles.activeTextGrey
                                    //                         :
                                    //                         styles.textGrey
                                    //                     :
                                    //                     styles.textGrey
                                    //                 :
                                    //                 selectedOptions.find(x => x.item_id === itemId)
                                    //                     ?
                                    //                     selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "CuttingOption").id == item.id
                                    //                         ?
                                    //                         styles.activeTextGrey
                                    //                         :
                                    //                         styles.textGrey
                                    //                     :
                                    //                     styles.textGrey
                                    //         } >{item.name}</Text>
                                    // </Pressable>
                                    <Pressable
                                        onPress={() => { setOptions(item); setCutId(item.id) }}
                                        style={
                                            itemId == 0
                                                ?
                                                selectedOptions.find(x => x.item_id === shared ? order.id : cart.id)
                                                    ?
                                                    selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "CuttingOption")
                                                        ?
                                                        selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "CuttingOption").id
                                                            ?
                                                            selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "CuttingOption").id == item.id
                                                                ?
                                                                styles.activeButton2
                                                                :
                                                                styles.Button2
                                                            :
                                                            styles.Button2
                                                        :
                                                        styles.Button2
                                                    :
                                                    styles.Button2
                                                :
                                                selectedOptions.find(x => x.item_id === itemId)
                                                    ?
                                                    selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "CuttingOption")
                                                        ?
                                                        selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "CuttingOption").id
                                                            ?
                                                            selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "CuttingOption").id == item.id
                                                                ?
                                                                styles.activeButton2
                                                                :
                                                                styles.Button2
                                                            :
                                                            styles.Button2
                                                        :
                                                        styles.Button2
                                                    :
                                                    styles.Button2
                                        } >
                                        <Text
                                            style={
                                                itemId == 0
                                                    ?
                                                    selectedOptions.find(x => x.item_id === shared ? order.id : cart.id)
                                                        ?
                                                        selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "CuttingOption")
                                                            ?
                                                            selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "CuttingOption").id
                                                                ?
                                                                selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "CuttingOption").id == item.id
                                                                    ?
                                                                    styles.activeTextGrey
                                                                    :
                                                                    styles.textGrey
                                                                :
                                                                styles.textGrey
                                                            :
                                                            styles.textGrey
                                                        :
                                                        styles.textGrey
                                                    :
                                                    selectedOptions.find(x => x.item_id === itemId)
                                                        ?
                                                        selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "CuttingOption")
                                                            ?
                                                            selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "CuttingOption").id
                                                                ?
                                                                selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "CuttingOption").id == item.id
                                                                    ?
                                                                    styles.activeTextGrey
                                                                    :
                                                                    styles.textGrey
                                                                :
                                                                styles.textGrey
                                                            :
                                                            styles.textGrey
                                                        :
                                                        styles.textGrey
                                            } >{item.name}</Text>
                                    </Pressable>
                                )
                            })
                            }
                        </View>
                    }
                    {preper && preper.length == 0
                        ?
                        <></>
                        :
                        <View style={{ marginVertical: '2%', flexDirection: 'column', width: '95%', alignSelf: "center", justifyContent: 'center', }}>
                            <Text style={styles.title2}>{strings('lang.processing')}</Text>
                            {preper && preper.map((item, index) => {
                                return (
                                    <Pressable
                                        onPress={() => { setOptions(item); setPreperId(item.id) }}
                                        style={
                                            itemId == 0
                                                ?
                                                selectedOptions.find(x => x.item_id === shared ? order.id : cart.id)
                                                    ?
                                                    selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "PreparingOption")
                                                        ?
                                                        selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "PreparingOption").id
                                                            ?
                                                            selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "PreparingOption").id == item.id
                                                                ?
                                                                styles.activeButton2
                                                                :
                                                                styles.Button2
                                                            :
                                                            styles.Button2
                                                        :
                                                        styles.Button2
                                                    :
                                                    styles.Button2
                                                :
                                                selectedOptions.find(x => x.item_id === itemId)
                                                    ?
                                                    selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "PreparingOption")
                                                        ?
                                                        selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "PreparingOption").id
                                                            ?
                                                            selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "PreparingOption").id == item.id
                                                                ?
                                                                styles.activeButton2
                                                                :
                                                                styles.Button2
                                                            :
                                                            styles.Button2
                                                        :
                                                        styles.Button2
                                                    :
                                                    styles.Button2
                                        } >
                                        <Text
                                            style={
                                                itemId == 0
                                                    ?
                                                    selectedOptions.find(x => x.item_id === shared ? order.id : cart.id)
                                                        ?
                                                        selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "PreparingOption")
                                                            ?
                                                            selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "PreparingOption").id
                                                                ?
                                                                selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "PreparingOption").id == item.id
                                                                    ?
                                                                    styles.activeTextGrey
                                                                    :
                                                                    styles.textGrey
                                                                :
                                                                styles.textGrey
                                                            :
                                                            styles.textGrey
                                                        :
                                                        styles.textGrey
                                                    :
                                                    selectedOptions.find(x => x.item_id === itemId)
                                                        ?
                                                        selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "PreparingOption")
                                                            ?
                                                            selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "PreparingOption").id
                                                                ?
                                                                selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "PreparingOption").id == item.id
                                                                    ?
                                                                    styles.activeTextGrey
                                                                    :
                                                                    styles.textGrey
                                                                :
                                                                styles.textGrey
                                                            :
                                                            styles.textGrey
                                                        :
                                                        styles.textGrey
                                            } >{item.name}</Text>
                                    </Pressable>
                                )
                            })
                            }
                        </View>
                    }

                    {head && head.length == 0
                        ?
                        <></>
                        :
                        <View style={{ marginVertical: '2%', flexDirection: 'column', width: '95%', alignSelf: "center", justifyContent: 'center', }}>
                            <Text style={styles.title2}>{strings('lang.head')}</Text>
                            {head && head.map((item, index) => {
                                return (
                                    <Pressable
                                        onPress={() => { setOptions(item); setPreperId(item.id) }}
                                        style={
                                            itemId == 0
                                                ?
                                                selectedOptions.find(x => x.item_id === shared ? order.id : cart.id)
                                                    ?
                                                    selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "HeadOption")
                                                        ?
                                                        selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "HeadOption").id
                                                            ?
                                                            selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "HeadOption").id == item.id
                                                                ?
                                                                styles.activeButton2
                                                                :
                                                                styles.Button2
                                                            :
                                                            styles.Button2
                                                        :
                                                        styles.Button2
                                                    :
                                                    styles.Button2
                                                :
                                                selectedOptions.find(x => x.item_id === itemId)
                                                    ?
                                                    selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "HeadOption")
                                                        ?
                                                        selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "HeadOption").id
                                                            ?
                                                            selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "HeadOption").id == item.id
                                                                ?
                                                                styles.activeButton2
                                                                :
                                                                styles.Button2
                                                            :
                                                            styles.Button2
                                                        :
                                                        styles.Button2
                                                    :
                                                    styles.Button2
                                        } >
                                        <Text
                                            style={
                                                itemId == 0
                                                    ?
                                                    selectedOptions.find(x => x.item_id === shared ? order.id : cart.id)
                                                        ?
                                                        selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "HeadOption")
                                                            ?
                                                            selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "HeadOption").id
                                                                ?
                                                                selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "HeadOption").id == item.id
                                                                    ?
                                                                    styles.activeTextGrey
                                                                    :
                                                                    styles.textGrey
                                                                :
                                                                styles.textGrey
                                                            :
                                                            styles.textGrey
                                                        :
                                                        styles.textGrey
                                                    :
                                                    selectedOptions.find(x => x.item_id === itemId)
                                                        ?
                                                        selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "HeadOption")
                                                            ?
                                                            selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "HeadOption").id
                                                                ?
                                                                selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "HeadOption").id == item.id
                                                                    ?
                                                                    styles.activeTextGrey
                                                                    :
                                                                    styles.textGrey
                                                                :
                                                                styles.textGrey
                                                            :
                                                            styles.textGrey
                                                        :
                                                        styles.textGrey
                                            } >{item.name}</Text>
                                        <Text
                                            style={
                                                itemId == 0
                                                    ?
                                                    selectedOptions.find(x => x.item_id === shared ? order.id : cart.id)
                                                        ?
                                                        selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "HeadOption")
                                                            ?
                                                            selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "HeadOption").id
                                                                ?
                                                                selectedOptions.find(x => x.item_id === shared ? order.id : cart.id).options.find(x => x.type === "HeadOption").id == item.id
                                                                    ?
                                                                    styles.activeTextGrey
                                                                    :
                                                                    styles.textGrey
                                                                :
                                                                styles.textGrey
                                                            :
                                                            styles.textGrey
                                                        :
                                                        styles.textGrey
                                                    :
                                                    selectedOptions.find(x => x.item_id === itemId)
                                                        ?
                                                        selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "HeadOption")
                                                            ?
                                                            selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "HeadOption").id
                                                                ?
                                                                selectedOptions.find(x => x.item_id === itemId).options.find(x => x.type === "HeadOption").id == item.id
                                                                    ?
                                                                    styles.activeTextGrey
                                                                    :
                                                                    styles.textGrey
                                                                :
                                                                styles.textGrey
                                                            :
                                                            styles.textGrey
                                                        :
                                                        styles.textGrey
                                            } >{item.price}</Text>
                                    </Pressable>
                                )
                            })
                            }
                        </View>
                    }
                    {shared
                        ?
                        <></>
                        :
                        <>
                            <Text style={styles.title}>{strings('lang.Ordernotes')}</Text>
                            <View style={{ height: screenHeight / 5.5, borderWidth: 1, borderRadius: 20, borderColor: MediumGrey, flexDirection: 'row', justifyContent: 'center', width: "90%", marginHorizontal: '5%', marginBottom: '5%' }} >
                                <Input onChangeText={(text) => setNote(text)}
                                    placeholderTextColor={'#707070'} placeholder={(strings('lang.Notes'))}
                                    style={styles.inputText} value={note}
                                />
                            </View>
                        </>
                    }

                    <View style={{ flexDirection: I18nManager.isRTL ? 'row' : 'row-reverse', alignItems: 'flex-end', justifyContent: "space-between", width: screenWidth / 1.1, alignSelf: 'center', height: screenHeight / 19, marginVertical: '10%' }}>
                        <Button
                            onPress={() => { back() }}
                            style={{ width: '45%', alignSelf: "center", height: 40, backgroundColor: WhiteGery, alignItems: "center", justifyContent: "center", borderRadius: screenWidth / 10, }}>
                            <Image source={require('../images/modrek/arrowRight.png')} style={{ transform: [{ scaleX: I18nManager.isRTL ? 1 : -1 }], tintColor: Black, resizeMode: 'contain', width: '15%', height: '55%', alignItems: 'center', transform: [{ scaleX: I18nManager.isRTL ? 1 : -1 }] }} />
                            <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: Black, marginHorizontal: '1%' }}>{strings('lang.back')}</Text>
                        </Button>
                        <Button onPress={() => { nav() }} style={{ width: '45%', alignSelf: "center", height: screenHeight / 18, backgroundColor: Red, alignItems: "center", flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', justifyContent: "center", borderRadius: 20 }}>
                            <Image source={require('../images/modrek/arrowLeft.png')} style={{ transform: [{ scaleX: I18nManager.isRTL ? 1 : -1 }], resizeMode: 'contain', width: '15%', height: '55%', alignItems: 'center', tintColor: White, transform: [{ scaleX: I18nManager.isRTL ? 1 : -1 }] }} />
                            <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: White, }}>{strings('lang.next')}</Text>
                        </Button>
                    </View>
                </KeyboardAwareScrollView>
                {!keyboardIsOpen &&
                    <>
                        <View style={{ height: screenHeight / 10, }}></View>
                        {/* <MyFooter navigation={props.navigation} /> */}
                    </>
                }

                <Modal
                    transparent={true}
                    animationType="fade"
                    visible={ModalVisible}
                >

                    <Button transparent onPress={() => setModalVisible(!ModalVisible)} style={{ backgroundColor: '#000', opacity: 0.5, width: screenWidth, height: screenHeight }} >
                    </Button>

                    <View style={styles.modal}>
                        <Text style={styles.modaltext}>{strings('lang.message17')}</Text>

                        <View style={{ flexDirection: 'row-reverse', alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', width: '85%', }}>
                            <Button style={styles.modalbuttonContainer} onPress={() => { setModalVisible(!ModalVisible) }}>
                                <Text style={styles.modalbuttonText}>{strings('lang.No')}</Text>
                            </Button>
                            <Button style={styles.modalbuttonContainer2} onPress={() => { clearCart() }}>
                                <Text style={styles.modalbuttonText2}>{strings('lang.Yes')}</Text>
                            </Button>
                        </View>

                    </View>
                </Modal>


            </View>
        )
    }
}


export default CompleteOrder0;
const styles = StyleSheet.create({
    input: {
        height: 35, borderWidth: .5, borderRadius: 20, borderColor: DarkGrey, flexDirection: 'row', justifyContent: 'center',
        width: "90%", marginBottom: 10,
    },
    inputText: {
        textAlign: I18nManager.isRTL ? 'right' : 'left', color: DarkGrey, width: '100%', fontFamily: appFont,
        fontSize: screenWidth / 28, alignSelf: 'flex-start'
    },
    textGrey: {
        color: Black,
        fontFamily: appFontBold,
        fontSize: screenWidth / 32,
        marginHorizontal: '3%'
    },
    textWhite: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 32,
        marginStart: 4
    },
    activeTextGrey: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 32,
        marginHorizontal: '3%'
    },
    title: {
        color: Black,
        fontFamily: appFontBold,
        fontSize: screenWidth / 24,
        marginStart: '5%',
        marginBottom: 5,
        alignSelf: 'flex-start'
    },
    title2: {
        color: Black,
        fontFamily: appFontBold,
        fontSize: screenWidth / 24,
        marginStart: '2.5%',
        marginBottom: 5,
        alignSelf: 'flex-start'
    },
    activeButton1: {
        flexDirection: 'row', height: '60%', backgroundColor: Red, borderRadius: 20,
        minWidth: screenWidth / 5,
        // width: screenWidth / 2,
        alignItems: "center", justifyContent: "center", alignSelf: 'center', marginHorizontal: 5,
    },
    activeLabel1: { fontFamily: appFont, fontSize: screenWidth / 35, color: White, marginHorizontal: 10 },
    Button1: { flexDirection: 'row', height: '60%', backgroundColor: WhiteGery, borderRadius: 15, minWidth: screenWidth / 5, alignItems: "center", justifyContent: "center", alignSelf: 'center', marginHorizontal: 5, borderColor: WhiteGery, borderWidth: 1 },
    label1: { fontFamily: appFont, fontSize: screenWidth / 35, color: 'black', marginHorizontal: 10 },

    activeButton2: { flexDirection: 'row', width: '100%', height: screenHeight / 22, borderWidth: 1, borderColor: MediumGrey, borderRadius: 20, marginBottom: 10, justifyContent: 'space-between', alignItems: "center", borderColor: WhiteGery, borderWidth: 0.8, backgroundColor: Red },
    activeLabel2: { fontFamily: appFont, fontSize: screenWidth / 30, color: White, marginHorizontal: 10 },
    Button2: { flexDirection: 'row', width: '100%', height: screenHeight / 22, borderWidth: 1, borderColor: MediumGrey, borderRadius: 20, marginBottom: 10, justifyContent: 'space-between', alignItems: "center", borderColor: WhiteGery, borderWidth: 0.8, backgroundColor: WhiteGery },
    label2: { fontFamily: appFont, fontSize: screenWidth / 30, color: 'black', marginHorizontal: 10 },

    redCircle: { width: screenWidth / 18, height: screenWidth / 18, borderRadius: screenWidth / 36, backgroundColor: Red, alignItems: 'center', justifyContent: 'center', marginStart: 5 },
    greenCircle: { width: screenWidth / 18, height: screenWidth / 18, borderRadius: screenWidth / 36, backgroundColor: appColor1, alignItems: 'center', justifyContent: 'center', marginStart: 5 },
    modal: {
        flex: 1,
        alignItems: 'center',
        width: screenWidth / 1.2,
        height: screenHeight / 5,
        position: 'absolute',
        top: screenHeight / 2.5,
        alignSelf: 'center',
        backgroundColor: White,
        borderRadius: 10,
        paddingVertical: '8%',
        justifyContent: 'space-between',
        paddingHorizontal: '5%'
    },
    modaltext: {
        fontFamily: appFontBold,
        color: Red,
        fontSize: screenWidth / 22,
        alignSelf: 'center',
        marginTop: '0%',
        textAlign: 'center'
    },
    modalbuttonContainer: {
        width: screenWidth / 4,
        height: '75%',
        backgroundColor: White,
        alignSelf: 'center',
        marginHorizontal: 5,
        justifyContent: 'center',
        borderRadius: 10,
        alignItems: 'center',
        padding: 12,
        marginTop: 7,
        borderWidth: 1,
        borderColor: Red,
    },
    modalbuttonContainer2: {
        width: screenWidth / 4,
        height: '75%',
        backgroundColor: Red,
        alignSelf: 'center',
        marginHorizontal: 5,
        justifyContent: 'center',
        borderRadius: 10,
        alignItems: 'center',
        padding: 12,
        marginTop: 7,
    },
    modalbuttonText2: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    modalbuttonText: {
        color: Red,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
});