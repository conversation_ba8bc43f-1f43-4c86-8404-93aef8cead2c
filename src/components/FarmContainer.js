import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { useEffect, useRef, useState } from 'react';
import { Animated, Image, Platform, Pressable, StyleSheet, Text, View } from "react-native";
import { VLCPlayer } from 'react-native-vlc-media-player';
import { useDispatch } from 'react-redux';
import * as farmesActions from '../../Store/Actions/farms';
import { strings } from '../screens/i18n';
import CircularProgress3 from './CircularProgress3';
import LoadingMoreSmall from './LoadingMoreSmall';
import { appFontBold, appFontNawar, appFontTajawal, Black, DarkGrey, DarkYellow, MediumGrey, Red, Red1, screenHeight, screenWidth, <PERSON>, WhiteGery } from './Styles';
import Toaster from './Toaster';


const FarmContainer = props => {
    const [visible, setIsVisible] = useState(false);

    const [fav, setFav] = useState(props.item.is_favorited);
    // const [subscriped, setSubscriped] = useState(props.item.trader.is_subscribed_to);
    const [loading, setLoading] = useState(false);
    const [ModalVisible, setModalVisible] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [price, setPrice] = useState(props.sheepTypeId == 2 ? (props.item.price / 2) : props.sheepTypeId == 3 ? (props.item.price / 4) : props.item.price);
    const dispatch = useDispatch();

    const [Token, setToken] = useState('');

    useEffect(() => {
        console.log('price', price);

        const token = async () => {
            let token = await AsyncStorage.getItem('token')
            setToken(token)
        }
        token()
    }, []);
    const skipLogin = () => {
        Toaster(
            'top',
            'warning',
            Red1,
            strings('lang.login_continuou'),
            White,
            1500,
            screenHeight / 50,
        );
        // props.navigation.navigate('Login')
    }
    const toggleFav = async () => {
        if (Token) {
            setLoadingMore(true)
            try {
                // if (token) {
                // if (fav == false) {
                let response = await dispatch(farmesActions.farmToggleFavorite(props.item.id));
                if (response.success == true) {
                    setFav(response.data.is_favorited)
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoadingMore(false)
            } catch (err) {
                setLoadingMore(false)
                console.log('err', err)
            }
        } else {
            skipLogin()
        }

    };

    const toggleSubscription = async () => {
        setLoadingMore(true)
        try {
            let response = await dispatch(farmesActions.traderoggleSubscription(props.item.trader.id));
            if (response.success == true) {
                setSubscriped(response.data.is_subscribed)
            } else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoadingMore(false)
        }
        catch (err) {
            setLoadingMore(false)
            console.log('err', err)
        }
    };

    const [isLoading, setIsLoading] = useState(true);


    useEffect(() => {
        setModalVisible(!ModalVisible)
        // console.log('Loading started');
        const timer = setTimeout(() => {
            setIsLoading(false);
            // console.log('Loading ended');
        }, Platform.OS == 'ios' ? 20000 : 6000);

        return () => clearTimeout(timer);
    }, []);

    const opacity = useRef(new Animated.Value(1)).current; // Initialize opacity as an Animated Value

    useEffect(() => {
        // Loop the animation between 1 (fully visible) and 0.5 (semi-transparent)
        const fadeAnimation = Animated.loop(
            Animated.sequence([
                Animated.timing(opacity, {
                    toValue: 0.5, // Target opacity
                    duration: 1000, // Duration in ms
                    useNativeDriver: true, // Optimize for native performance
                }),
                Animated.timing(opacity, {
                    toValue: 1, // Reset to full opacity
                    duration: 1000, // Duration in ms
                    useNativeDriver: true,
                }),
            ])
        );

        fadeAnimation.start(); // Start the animation

        return () => fadeAnimation.stop(); // Cleanup on unmount
    }, [opacity]);


    return (
        <Pressable onPress={
            props.fav
                ?
                props.livePress
                :
                props.openLivePress
        } style={{
            // height: screenHeight / 8,
            paddingVertical: 10,
            paddingBottom: props.item.id == props.farmId ? 25 : 0,
            height: screenHeight / 7,
            flexWrap: 'wrap',
            marginVertical: '4%',
            backgroundColor: props.item.id == props.farmId ? '#B3BFC4' : White, alignSelf: "center",
            marginTop: 0, width: '95%', flexDirection: 'row', justifyContent: 'space-between',
            // borderBottomWidth: props.item.id == props.farmId ? 1 : 0,
            borderRadius: 10,
            // borderColor: MediumGrey,
            // borderWidth: props.item.id == props.farmId ? 0 : 1.5,
            paddingHorizontal: 5,
            // Shadow for iOS
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.25,
            shadowRadius: 3.84,

            // Shadow for Android
            elevation: 5,

            // Important Fixes
            overflow: Platform.OS === 'android' ? 'hidden' : props.item.id == props.farmId ? 'hidden' : 'visible', // avoid shadow clipping
            zIndex: 1, // lift the item visually
        }}>

            <Pressable onPress={props.fav
                ?
                props.livePress
                :
                props.openLivePress
            } style={{
                //  backgroundColor: props.item.id == props.itemId ? White : Red,
                width: '30%', height: '90%', overflow: 'hidden', alignItems: 'center', justifyContent: 'center',
                alignSelf: 'center',
                borderRadius: 5,
                borderColor: props.item.id == props.farmId ? White : '#b5bfc4',
                borderWidth: props.item.id == props.farmId ? 0 : 1,
                backgroundColor: props.item.id == props.farmId ? White : '#dfe5e8',
                // opacity: .6
            }}>
                {props.item.id == props.farmId ?
                    <>
                        {
                            isLoading ? (
                                <>
                                    <View style={{
                                        // transform: [{ rotate: '90deg' }],
                                        position: 'absolute', height: '100%', width: '100%', alignItems: 'center',
                                        justifyContent: 'center',
                                    }}>
                                        {/* <Text style={{ color: White, marginBottom: 5, fontFamily: appFontNawar, fontSize: screenWidth / 35 }}>{'برجاء الانتظار جاري تحميل البث...'}</Text> */}
                                        <CircularProgress3 size={100} strokeWidth={10}
                                            duration={Platform.OS == 'ios' ? 20000 : 8000} />

                                    </View>

                                </>
                            ) : (
                                <></>
                            )

                        }
                        <VLCPlayer
                            style={[styles.backgroundVideo]}
                            source={{
                                initType: 2,
                                hwDecoderEnabled: 1,
                                hwDecoderForced: 1,
                                uri: props.item.camera_url,
                                initOptions: [
                                    '--no-audio',
                                    '--rtsp-tcp',
                                    '--network-caching=150',
                                    '--rtsp-caching=150',
                                    '--no-stats',
                                    '--tcp-caching=150',
                                    '--realrtsp-caching=150',
                                ],
                            }} videoAspectRatio="16:9"
                            // autoAspectRatio={true}
                            isLive={true}
                            autoplay={true}
                            autoReloadLive={true}
                            initOptions={[
                                "--rtsp-tcp",
                                "--network-caching=" + 0,
                                "--rtsp-caching=" + 0,
                                "--no-stats",
                                "--tcp-caching=" + 0,
                                "--realrtsp-caching=" + 0,
                            ]}
                            hwDecoderEnabled={1}
                            hwDecoderForced={1}
                            mediaOptions={{
                                // ':network-caching': 0,
                                // ':live-caching': 300,
                            }}
                            onError={(err) => console.log("video error:", err)}
                        // resizeMode='contain'
                        />

                    </>
                    :
                    <>
                        {/* {props.item.image &&
                            <Image source={{ uri: props.item.image }} style={{ width: '100%', resizeMode: 'cover', height: '100%', alignSelf: 'center' }} />
                        } */}

                        {/* {
                            props.fav ?
                                <></>
                                : */}
                        <Image source={require('../images/modrek/play.png')} style={{
                            width: screenWidth / 10, height: screenHeight / 18, zIndex: 1000, resizeMode: 'contain', position: 'absolute',
                            right: '35%', top: '25%', opacity: 1, tintColor: Red
                        }} />
                        {/* } */}
                    </>
                }

                <Image source={require('../images/newIcon/liveIconFarm1.png')} style={{ width: screenWidth / 12, height: screenHeight / 20, zIndex: 10000000, resizeMode: 'contain', position: 'absolute', right: 5, top: -10 }} />


            </Pressable>
            {/* </Modal> */}

            {loadingMore ? <LoadingMoreSmall style={{}} /> : <View></View>}
            {/* svgexport612 */}

            <View style={{ width: '68%', height: '100%', justifyContent: 'center' }}>
                <View style={{ width: '100%', height: '30%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', }}>
                        <View style={{
                            height: screenHeight / 35,
                            width: screenHeight / 35,
                            borderRadius: screenHeight / 70,
                            borderWidth: 1,
                            borderColor: Red,
                            justifyContent: 'center',
                            alignItems: 'center',
                            overflow: 'hidden'
                        }}>
                            {/* <Image source={require('../images/newIcon/logo.png')} style={{ width: '100%', height: '100%', zIndex: 1000, resizeMode: 'cover', }} /> */}
                            <Image source={{ uri: props.item.image }} style={{ width: '100%', height: '100%', zIndex: 1000, resizeMode: 'cover', }} />
                        </View>
                        <Text style={{ fontFamily: appFontTajawal, fontSize: screenWidth / 35, marginHorizontal: '2%', color: Red }}>{props.item.code}</Text>
                    </View>
                    <Pressable onPress={props.traderPress} transparent style={{ marginEnd: '5%', height: screenHeight / 35, width: screenWidth / 4, overflow: 'hidden', alignItems: 'center', justifyContent: 'center', flexDirection: 'row' }}>
                        <Text style={{ fontFamily: appFontTajawal, fontSize: screenWidth / 40, marginHorizontal: '5%', color: DarkGrey }}>{strings('lang.AllContent') + ` (${props.item.trader.farms_count})`}</Text>
                        <Image source={require('../images/modrek/surface-9000.png')} style={{ width: '10%', resizeMode: 'contain', alignSelf: 'center', height: '55%', tintColor: DarkGrey, }} />
                    </Pressable>
                    {/* <TouchableOpacity onPress={props.detailsPress} transparent style={{ marginEnd: '5%', backgroundColor: Red, height: screenHeight / 35, width: screenHeight / 35, overflow: 'hidden', borderRadius: screenHeight / 70, alignItems: 'center', justifyContent: 'center', }}>
                        <Text style={{ fontFamily: appFontNawar, fontSize: screenWidth / 30, color: White, }}>{'i'}</Text>
                    </TouchableOpacity> */}
                    {/* <Text style={{ fontFamily: appFontNawar, fontSize: screenWidth / 40, marginHorizontal: '2%', color: DarkGrey }}>{strings('lang.AllContent') + ` (${props.item.trader.farms_count})`}</Text>
                    <TouchableOpacity onPress={props.traderPress} transparent style={{ backgroundColor: WhiteGery, width: screenHeight / 40, height: screenHeight / 40, borderRadius: screenHeight / 80, alignItems: 'center', justifyContent: 'center' }}>
                        <Image source={require('../images/modrek/surface-9000.png')} style={{ width: '55%', resizeMode: 'contain', alignSelf: 'center', height: '55%', tintColor: DarkGrey, }} />
                    </TouchableOpacity> */}

                </View>
                <View style={{ width: '100%', height: '30%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <Pressable onPress={props.detailsPress} style={{
                            flexDirection: 'row', alignItems: 'center', width: screenHeight / 20
                        }}>
                            <Image source={require('../images/modrek/svgexport-133.png')} style={{ width: screenWidth / 25, resizeMode: 'contain', height: screenHeight / 30, marginEnd: '5%', }} />
                            <Text style={{
                                fontFamily: appFontBold, fontSize: screenWidth / 35, color: DarkGrey,
                                // marginStart: 5 
                            }}>{props.item.rating == 0 ? '4.5' : props.item.rating}</Text>
                        </Pressable>

                        <View style={{ width: 1, height: screenHeight / 50, backgroundColor: DarkGrey, marginEnd: '8%' }}></View>
                        <Text style={{
                            marginHorizontal: 2, fontFamily: appFontTajawal, fontSize: screenWidth / 30, color: Red,
                            // lineHeight: screenHeight / 23
                        }}>
                            {`${props.item.weight && (props.sheepTypeId == 2 ? props.item.weight.from / 2 : props.sheepTypeId == 3 ? props.item.weight.from / 4 : props.item.weight.from)} - ${props.item.weight && (props.sheepTypeId == 2 ? props.item.weight.to / 2 : props.sheepTypeId == 3 ? props.item.weight.to / 4 : props.item.weight.to)}`}
                        </Text>
                        {props.farmId == props.item.id
                            ?
                            <Image source={require('../images/newIcon/activeWeight1.png')} style={{
                                marginEnd: 2, width: screenWidth / 20,
                                resizeMode: 'contain', height: screenHeight / 30,
                                marginEnd:
                                    props.item.sheep_category && props.item.sheep_category.name == "سواكني ابيض " ?
                                        '3%'
                                        :
                                        '8%',
                            }} />
                            :
                            <Image source={require('../images/newIcon/weight11.png')} style={{
                                marginEnd: 2, width: screenWidth / 20, resizeMode: 'contain',
                                height: screenHeight / 30,
                                marginHorizontal: props.item.sheep_category && props.item.sheep_category.name == "سواكني ابيض " ?
                                    '3%'
                                    :
                                    '8%',
                            }} />


                        }

                        <Text style={{
                            fontFamily: appFontTajawal, fontSize: screenWidth / 38, color: Red,
                            // lineHeight: screenHeight / 20
                        }}>
                            {`${props.sheepTypeId && props.sheepTypeId == 3
                                ? 'ربع'
                                :
                                props.sheepTypeId == 2 ?
                                    'نصف'
                                    :
                                    'كامل'
                                } ${props.item.sheep_category && props.item.sheep_category.name}`}
                        </Text>
                        <Image source={require('../images/newIcon/sheepIcon.png')} style={{ width: screenWidth / 20, resizeMode: 'contain', height: screenHeight / 30, marginHorizontal: '1.5%', }} />

                    </View>
                    {/* <View> */}

                    {/* </View> */}
                </View>
                <View style={{ width: '100%', height: '40%', flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start' }}>

                    {props.farmId == props.item.id
                        ?
                        <>
                            <View style={{ flexDirection: 'row', alignItems: 'center', width: '55%' }}>
                                {
                                    props.item.discount
                                        // props.item.price_before_discount
                                        ?
                                        <>
                                            <Text style={{ fontFamily: appFontTajawal, fontSize: screenWidth / 30, color: Red, textDecorationLine: 'line-through', }}>
                                                {`${props.sheepTypeId == 2 ? (props.item.price_before_discount / 2) : props.sheepTypeId == 3 ? (props.item.price_before_discount / 4) : props.item.price_before_discount} `}</Text>
                                            <Image source={require('../images/newIcon/descount.png')} style={{ marginHorizontal: 2, width: screenWidth / 20, resizeMode: 'contain', height: screenHeight / 30, marginEnd: '2%', }} />
                                            <Text style={{ fontFamily: appFontTajawal, fontSize: screenWidth / 27, color: Red1 }}>
                                                {`${props.sheepTypeId == 2 ? (props.item.price / 2) : props.sheepTypeId == 3 ? (props.item.price / 4) : props.item.price} `}</Text>
                                            <Image source={require('../images/newIcon/priceIcon.png')} style={{ marginHorizontal: 2, width: screenWidth / 15, resizeMode: 'cover', height: screenHeight / 30, marginEnd: '2%', }} />

                                        </>
                                        :
                                        <>
                                            <Text style={{ fontFamily: appFontTajawal, fontSize: screenWidth / 30, color: Red, }}>
                                                {`${props.sheepTypeId == 2 ? (props.item.price / 2) : props.sheepTypeId == 3 ? (props.item.price / 4) : props.item.price} `}</Text>
                                            <Image source={require('../images/newIcon/descount.png')} style={{ marginHorizontal: 2, width: screenWidth / 20, resizeMode: 'contain', height: screenHeight / 30, marginEnd: '2%', }} />
                                        </>
                                }


                            </View>

                            {/* // <View style={{ width: '27%', height: '100%', justifyContent: 'center', alignItems: 'center', position: 'absolute', right: 20 }}> */}
                            <Animated.View style={{ opacity, width: '43%', alignSelf: 'center' }}>
                                <Pressable onPress={props.livePress} transparent style={{ backgroundColor: Red1, height: screenHeight / 30, width: '100%', overflow: 'hidden', borderRadius: screenHeight / 20, alignSelf: 'flex-end', alignItems: 'center', justifyContent: 'center', }}>
                                    <Text style={{ fontFamily: appFontTajawal, fontSize: screenWidth / 30, color: White, }}>{props.reservation ? strings('lang.MoveToAddareservation') : strings('lang.Gotobookingg')}</Text>
                                </Pressable>
                            </Animated.View>
                        </>

                        :
                        <>
                            <Pressable onPress={props.detailsPress} transparent style={{ height: screenHeight / 35, width: screenHeight / 29, overflow: 'hidden', alignItems: 'center', justifyContent: 'center', }}>
                                {/* <Text style={{ fontFamily: appFontNawar, fontSize: screenWidth / 30, color: White, }}>{'i'}</Text> */}
                                <Image source={require('../images/newIcon/details1.png')} style={{ width: '100%', resizeMode: 'contain', height: screenHeight / 30, }} />
                            </Pressable>

                            <View style={{
                                height: screenHeight / 35, width: '70%', flexDirection: 'row', borderRadius: 10, overflow: 'hidden', alignItems: 'center', justifyContent: 'flex-start', paddingHorizontal: 8
                            }}>
                                <View style={{ width: 1, height: screenHeight / 50, backgroundColor: DarkGrey, marginHorizontal: Platform.OS == 'ios' ? '3.5%' : '3%' }}></View>
                                {props.item.discount
                                    ?
                                    <>
                                        <Text style={{ fontFamily: appFontTajawal, fontSize: screenWidth / 30, color: Red, textDecorationLine: 'line-through', }}>
                                            {`${props.sheepTypeId == 2 ? (props.item.price_before_discount / 2) : props.sheepTypeId == 3 ? (props.item.price_before_discount / 4) : props.item.price_before_discount} `}</Text>
                                        <Image source={require('../images/newIcon/descount.png')} style={{ marginHorizontal: 2, width: screenWidth / 20, resizeMode: 'contain', height: screenHeight / 30, marginEnd: '5%', }} />
                                        <Text style={{ fontFamily: appFontTajawal, fontSize: screenWidth / 27, color: Red1 }}>
                                            {`${props.sheepTypeId == 2 ? (props.item.price / 2) : props.sheepTypeId == 3 ? (props.item.price / 4) : props.item.price} `}</Text>
                                        <Image source={require('../images/newIcon/priceIcon.png')} style={{ marginHorizontal: 2, width: screenWidth / 15, resizeMode: 'cover', height: screenHeight / 30, marginEnd: '5%', }} />

                                    </>
                                    :
                                    <>
                                        <Text style={{ fontFamily: appFontTajawal, fontSize: screenWidth / 30, color: Red, }}>
                                            {`${props.sheepTypeId == 2 ? (props.item.price / 2) : props.sheepTypeId == 3 ? (props.item.price / 4) : props.item.price} `}</Text>
                                        <Image source={require('../images/newIcon/descount.png')} style={{ marginHorizontal: 2, width: screenWidth / 20, resizeMode: 'contain', height: screenHeight / 30, marginEnd: '5%', }} />
                                    </>
                                }


                            </View>


                            <Pressable onPress={() => { toggleFav() }} transparent style={{ height: screenHeight / 35, width: screenHeight / 35, overflow: 'hidden', alignItems: 'center', justifyContent: 'center', }}>
                                <Image source={fav ? require('../images/modrek/bookk.png') : require('../images/modrek/book.png')} style={{ resizeMode: 'contain', alignSelf: 'center', width: '100%', height: '100%', tintColor: fav ? DarkYellow : DarkGrey }} />
                            </Pressable>
                        </>
                    }


                </View>

            </View>
            {props.item.id == props.farmId ?
                <View style={{
                    width: '105%',
                    paddingHorizontal: 5,
                    height: '25%',
                    backgroundColor: Red,
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'row',
                    position: 'absolute',
                    bottom: 0
                }}>
                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: White, }}>
                        {`قسط ${(price / 4).toFixed(0)} `}
                    </Text>
                    <Image source={require('../images/newIcon/descount.png')} style={{
                        width: '8%', height: '70%', resizeMode: 'contain', tintColor: White
                    }} />
                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: White, }}>
                        {`/ شهريآ مع   `}
                    </Text>
                    <Image source={require('../images/newIcon/tamaralogo.png')} style={{
                        width: '15%', height: '70%', resizeMode: 'contain',
                    }} />
                </View>
                :
                <></>
            }

        </Pressable>
    )
};

export default FarmContainer;
const styles = StyleSheet.create({
    backgroundVideo: {
        width: '155%',
        height: '100%',
        justifyContent: 'center',
        // backgroundColor: White,
        // position: 'absolute',
        // top: -20,
        // left: 0,
        // bottom: 0,
        // right: 0,
        zIndex: 10000,
    },
    closeButton: {
        position: 'absolute',
        top: 50, // Adjust as needed
        right: 20, // Adjust as needed
        backgroundColor: Black,
        padding: 10,
        width: screenHeight / 22,
        height: screenHeight / 22,
        borderRadius: screenHeight / 44,
        zIndex: 10000000,
        alignItems: 'center',
        justifyContent: 'center',

    },
    closeButtonText: {
        color: 'white',
        fontSize: 16,
    },
});




