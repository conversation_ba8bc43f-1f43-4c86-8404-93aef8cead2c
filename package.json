{"name": "ModrkClient", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint ."}, "dependencies": {"@miblanchard/react-native-slider": "^2.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/clipboard": "^1.5.1", "@react-native-community/datetimepicker": "^3.0.2", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-firebase/app": "^22.4.0", "@react-native-firebase/messaging": "^22.4.0", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "add": "^2.0.6", "babel-plugin-transform-remove-console": "^6.9.4", "crypto-js": "^4.1.1", "i": "^0.3.7", "jwt-decode": "^3.1.2", "link": "^1.5.1", "moment": "^2.29.4", "native-base": "^2.13.14", "npm": "^8.18.0", "react": "^19.1.0", "react-native": "^0.80.1", "react-native-appsflyer": "^6.14.2-rc1", "react-native-audio-recorder-player": "^3.5.1", "react-native-background-timer": "^2.4.1", "react-native-camera": "^4.2.1", "react-native-confirmation-code-input": "^1.0.4", "react-native-copilot": "^2.5.1", "react-native-device-info": "^10.3.0", "react-native-document-picker": "^8.1.1", "react-native-fast-image": "^8.5.11", "react-native-geocoding": "^0.5.0", "react-native-geolocation-service": "^5.3.0", "react-native-gesture-handler": "^1.10.3", "react-native-google-places-autocomplete": "^2.5.6", "react-native-i18n": "^2.0.15", "react-native-image-picker": "^4.10.2", "react-native-image-resizer": "^1.4.5", "react-native-image-viewing": "^0.2.2", "react-native-image-zoom-viewer": "^3.0.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.6.2", "react-native-maps": "^1.1.0", "react-native-modalize": "^2.1.1", "react-native-pager-view": "^5.4.25", "react-native-permissions": "^3.6.1", "react-native-popover-view": "^5.1.2", "react-native-push-notification": "^8.1.1", "react-native-qrcode-scanner": "^1.5.5", "react-native-raw-bottom-sheet": "^2.2.0", "react-native-render-html": "^6.3.4", "react-native-restart": "^0.0.24", "react-native-safe-area-context": "^4.3.1", "react-native-screens": "^3.15.0", "react-native-select-dropdown": "^3.2.1", "react-native-skeleton-placeholder": "^5.0.0", "react-native-slider": "^0.11.0", "react-native-snap-carousel": "^3.9.1", "react-native-sound": "^0.11.2", "react-native-star-rating": "^1.1.0", "react-native-svg": "^13.0.0", "react-native-swipe-list-view": "^3.2.9", "react-native-video": "^5.2.1", "react-native-vlc-media-player": "^1.0.70", "react-native-webview": "^11.22.7", "react-redux": "^8.0.2", "redux": "^4.2.0", "redux-saga": "^1.1.3", "redux-thunk": "^2.4.1", "rn-fetch-blob": "^0.12.0"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/runtime": "^7.27.6", "@react-native-community/cli": "^19.1.0", "@react-native-community/eslint-config": "2.0.0", "@react-native/babel-preset": "^0.80.1", "@react-native/eslint-config": "^0.80.1", "@react-native/metro-config": "^0.80.1", "babel-jest": "26.6.3", "eslint": "7.32.0", "eslint-plugin-prettier": "^3.1.1", "jest": "^30.0.4", "metro-react-native-babel-preset": "0.67.0", "prettier": "^1.18.2", "react-test-renderer": "17.0.2"}, "jest": {"preset": "react-native"}}