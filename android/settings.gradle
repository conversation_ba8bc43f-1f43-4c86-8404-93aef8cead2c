rootProject.name = 'ModrkClient'
// All packages are auto-linked in React Native 0.80.1
// Auto-linking is handled by React Native 0.80.1 automatically
include ':app'
includeBuild('../node_modules/@react-native/gradle-plugin')

// include ':react-native-vector-icons'
// project(':react-native-vector-icons').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-vector-icons/android')

if (settings.hasProperty("newArchEnabled") && settings.newArchEnabled == "true") {
    include(":ReactAndroid")
    project(":ReactAndroid").projectDir = file('../node_modules/react-native/ReactAndroid')
}
